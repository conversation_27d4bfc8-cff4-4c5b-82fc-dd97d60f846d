using Fusion;
using UnityEngine;

namespace Projectiles
{
    /// <summary>
    /// A base for all weapon components. Holds references needed for proper weapon fire processing.
    /// </summary>
    public abstract class WeaponComponent : ContextBehaviour
    {
        // PUBLIC MEMBERS

        public byte WeaponActionId { get; set; }
        public Weapon Weapon { get; set; }
        public Transform BarrelTransform { get; set; }
        public WeaponContext WeaponContext { get; set; }

        /// <summary>
        /// NetworkButtons based on the current WeaponContext.
        /// </summary>
        public NetworkButtons Buttons => WeaponContext?.Buttons ?? default;
        public NetworkButtons PressedButtons => WeaponContext?.PressedButtons ?? default;
        public Transform FireTransform => WeaponContext?.FireTransform;

        /// <summary>
        /// Indicates whether this component is busy (e.g., during a reload).
        /// </summary>
        public virtual bool IsBusy => false;

        // PUBLIC METHODS

        /// <summary>
        /// Determines if the weapon can fire based on the current WeaponContext.
        /// </summary>
        /// <param name="context">The WeaponContext to use for firing.</param>
        /// <returns>True if the weapon can fire; otherwise, false.</returns>
        public virtual bool CanFire(WeaponContext context) => true;

        /// <summary>
        /// Processes the firing action using the current WeaponContext.
        /// </summary>
        /// <param name="context">The WeaponContext to use for firing.</param>
        public virtual void Fire(WeaponContext context) { }

        /// <summary>
        /// Visualizes the firing process, such as muzzle flashes or other effects, using the current WeaponContext.
        /// </summary>
        /// <param name="context">The WeaponContext to use for rendering the fire action.</param>
        public virtual void FireRender(WeaponContext context) { }

        /// <summary>
        /// Sets the WeaponContext for this weapon component.
        /// </summary>
        /// <param name="weaponContext">The context to set for this component.</param>
        public virtual void SetWeaponContext(WeaponContext weaponContext)
        {
            WeaponContext = weaponContext;
        }
    }
}
