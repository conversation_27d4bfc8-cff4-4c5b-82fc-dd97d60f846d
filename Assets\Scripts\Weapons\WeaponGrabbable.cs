using Projectiles;
using UnityEngine;

public class WeaponGrabbable : MonoBehaviour
{
    [SerializeField]
    private Rigidbody weaponRigidbody;
    [SerializeField]
    private NetworkPhysicsGrabbable physicsGrabbable;
    [SerializeField]
    private Weapon weapon;
    [SerializeField]
    private WeaponAimVisual weaponAimVisual;



    [SerializeField,HideInInspector]
    private NetworkHand currentOwningHand;

    public void OnGrab()
    {
        currentOwningHand = null;

       // transform.SetParent(physicsGrabbable.CurrentGrabber.transform);

        weaponAimVisual.TurnON();

        if (physicsGrabbable.CurrentGrabber)
        {
            var grabbingHand = physicsGrabbable.CurrentGrabber.hand;

            // Clear any existing weapon from the hand first
            if (grabbingHand.currentWeapon != null && grabbingHand.currentWeapon != weapon)
            {
                Debug.LogWarning("<color=yellow>OnGrab: Hand already has a weapon, clearing it first</color>");
                var oldWeapon = grabbingHand.currentWeapon;
                oldWeapon.SetWeaponContext(null, EquippedHand.none);
                oldWeapon.DisarmWeapon();

                // Reset old weapon's reload state if needed
                if (oldWeapon.weaponAnimator && oldWeapon.weaponAnimator.reloadPhase > 0)
                {
                    oldWeapon.weaponAnimator.ResetReloadState();
                }
            }

            // Set this weapon to the hand
            grabbingHand.currentWeapon = weapon;
            currentOwningHand = grabbingHand;

            // Set weapon owner for proper state tracking
            if (weapon.owningPlayer == null && grabbingHand.playerAgent != null)
            {
                weapon.SetOwner(grabbingHand.playerAgent);
                Debug.Log("<color=green>OnGrab: Set weapon owner to " + grabbingHand.playerAgent.name + "</color>");
            }

            Debug.Log("<color=green>OnGrab: Successfully grabbed weapon " + weapon.name + " with hand</color>");
        }
        else
        {
            Debug.LogError("<color=red>OnGrab: Network Grabber NOT FOUND!!</color>");
        }
    }

    public void OnUnGrab()
    {
        if (currentOwningHand)
        {
            weaponAimVisual.TurnOFF();
            var handWeapon = currentOwningHand.currentWeapon;

            if (handWeapon != null)
            {
                // Verify this is the correct weapon being ungrabbed
                if (handWeapon == weapon)
                {
                    Debug.Log("<color=green>OnUnGrab: Properly ungrabbing weapon " + weapon.name + " from hand</color>");

                    // Reset weapon reload state if it was in the middle of reloading
                    if (weapon.weaponAnimator && weapon.weaponAnimator.reloadPhase > 0)
                    {
                        weapon.weaponAnimator.ResetReloadState();
                    }

                    weapon.SetWeaponContext(null, EquippedHand.none);
                    weapon.DisarmWeapon();
                    currentOwningHand.currentWeapon = null;
                }
                else
                {
                    Debug.LogWarning("<color=yellow>OnUnGrab: Weapon mismatch - hand has different weapon than expected</color>");
                }
            }
            else
            {
                Debug.LogWarning("<color=yellow>OnUnGrab: Current weapon in hand is null</color>");
            }

            currentOwningHand = null;
        }
        else
        {
            Debug.LogWarning("<color=yellow>OnUnGrab: Current network hand is null</color>");
        }

       // transform.SetParent(null);
    }
}
