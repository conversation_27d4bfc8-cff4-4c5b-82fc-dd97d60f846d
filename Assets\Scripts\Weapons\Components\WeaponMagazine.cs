using Fusion;
using UnityEngine;

namespace Projectiles
{
    /// <summary>
    /// Handles weapon action ammo.
    /// </summary>
    public class WeaponMagazine : WeaponComponent
    {
        // PUBLIC MEMBERS

        public bool IsReloading => _isReloading;
        public float ReloadProgress => IsReloading
            ? _reloadCooldown.RemainingTime(Runner).Value / _reloadTime
            : 0f;
        public bool HasMagazine => _hasMagazine;
        public bool HasUnlimitedAmmo => _hasUnlimitedAmmo;
        public int MagazineAmmo => _magazineAmmo;
        public int WeaponAmmo => _weaponAmmo;
        public bool physicalReloadInProgress = false;

        // PRIVATE MEMBERS
        //[SerializeField]
        //private bool _autoReload = false;
        [SerializeField]
        private int _initialAmmo = 150;
        [SerializeField]
        private bool _hasMagazine = true;
        [SerializeField]
        private int _maxMagazineAmmo = 30;
        [SerializeField]
        private int _maxWeaponAmmo = 120;
        [SerializeField]
        private bool _hasUnlimitedAmmo;
        [SerializeField]
        private float _reloadTime = 2f;

        [Networked]
        private NetworkBool _isReloading { get; set; }
        [Networked]
        private int _magazineAmmo { get; set; }
        [Networked]
        private int _weaponAmmo { get; set; }
        [Networked]
        private TickTimer _reloadCooldown { get; set; }

        [Tooltip("Physics network grabable object, that player spawns from ammo pouch and places into weapon")]
        public GameObject physicalMagazine;
        [Tooltip("Magazine prefab that is just mesh - used as a dummy object until Network object can spawn and initalise")]
        public GameObject dummyMagazinePrefab;



        public WeaponAnimator weaponAnimator;

        // WeaponComponent INTERFACE

        /// <summary>
        /// Indicates whether the component is currently performing a reloading operation.
        /// </summary>
        public override bool IsBusy => _reloadCooldown.ExpiredOrNotRunning(Runner) == false;

        /// <summary>
        /// Determines if the weapon can fire based on the current context.
        /// </summary>
        /// <param name="context">The current WeaponContext.</param>
        /// <returns>True if the weapon can fire; otherwise, false.</returns>
        public override bool CanFire(WeaponContext context)
        {
            if (_isReloading)
                return false;

            int availableAmmo = _hasMagazine ? _magazineAmmo : _weaponAmmo;
            return availableAmmo > 0;
        }

        /// <summary>
        /// Processes the firing action based on the current context.
        /// </summary>
        /// <param name="context">The current WeaponContext.</param>
        public override void Fire(WeaponContext context)
        {
            if (_hasMagazine)
            {
                _magazineAmmo--;

            }
            else if (!_hasUnlimitedAmmo)
            {
                _weaponAmmo--;
            }

            // Debug.Log("AMMO COUNT " + WeaponAmmo);
        }

        /// <summary>
        /// Assigns the WeaponContext to this WeaponMagazine and its components.
        /// </summary>
        /// <param name="weaponContext">The context to assign.</param>
        //public override void SetWeaponContext(WeaponContext weaponContext)
        //{
        //    if (_currentContext == weaponContext)
        //        return;

        //    _currentContext = weaponContext;

        //    // You can perform additional setup here if necessary based on the context
        //}

        // NetworkBehaviour INTERFACE

        /// <summary>
        /// Called on network spawn. Initializes ammo counts based on whether the weapon has unlimited ammo or a magazine.
        /// </summary>
        public override void Spawned()
        {
            ResetAmmoCount();
        }

        private void Awake()
        {
            Debug.Assert(weaponAnimator, "No animator assigned in WeaponMagazine.cs " + this.gameObject.name);
        }

        public void ResetAmmoCount()
        {
            int initialAmmo = _hasUnlimitedAmmo ? int.MaxValue : _initialAmmo;

            _magazineAmmo = _hasMagazine
                ? Mathf.Clamp(initialAmmo, 0, _maxMagazineAmmo)
                : 0;
            _weaponAmmo = Mathf.Clamp(initialAmmo - _magazineAmmo, 0, _maxWeaponAmmo);
        }

        /// <summary>
        /// Called on each network FixedUpdate. Handles reloading logic based on the current context.
        /// </summary>
        public override void FixedUpdateNetwork()
        {
            //if (_isReloading/* && _reloadCooldown.Expired(Runner)*/)
            //{
            //    FinishReload();
            //}

            //if (ShouldReload())
            //{
            //    StartReload();
            //}
        }

        // PRIVATE METHODS

        /// <summary>
        /// Determines whether the weapon should initiate a reload based on the current context and ammo counts.
        /// </summary>
        /// <returns>True if a reload should be initiated; otherwise, false.</returns>
        public bool ShouldReload()
        {
            if (_isReloading)
                return false;

            //This prevents player from dropping mag until mag is empty, might want to remove this
            //if (MagazineAmmo > 0)
            //    return false;

            if (!_hasMagazine)
                return false; // Weapon without reloading capability

            if (_magazineAmmo >= _maxMagazineAmmo)
                return false; // Magazine already full

            if (_weaponAmmo <= 0)
                return false; // No ammo available to reload

            //bool reloadRequested = WeaponContext.Buttons.IsSet(EInputButton.Reload) || (_autoReload && _magazineAmmo == 0);
            //if (!reloadRequested)
            //   return false;

            return !Weapon.IsBusy();
        }

        /// <summary>
        /// Initiates the reloading process by setting the reload cooldown and updating the reloading state.
        /// </summary>
        public void StartReload()
        {
            //_reloadCooldown = TickTimer.CreateFromSeconds(Runner, _reloadTime);


            //_isReloading = true;
        }

        public void SetIsReloading()
        {
            _isReloading = true;

        }
        /// <summary>
        /// Completes the reloading process by transferring ammo from the weapon reserve to the magazine.
        /// </summary>
        public void FinishReload()
        {
            Debug.Log("Finsih reload");
            int reloadAmmo = _maxMagazineAmmo - _magazineAmmo;

            if (!_hasUnlimitedAmmo)
            {
                reloadAmmo = Mathf.Min(reloadAmmo, _weaponAmmo);
                _weaponAmmo -= reloadAmmo;
            }

            _magazineAmmo += reloadAmmo;

            _isReloading = false;
        }
    }
}
