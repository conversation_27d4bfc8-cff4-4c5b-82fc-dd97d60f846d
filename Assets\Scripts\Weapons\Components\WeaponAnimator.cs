using UnityEngine;
using Projectiles;
using Fusion;
using System.Collections;


public class WeaponAnimator : WeaponComponent
{

    public delegate void ReloadPhaseChanged(int newPhase);
    public event ReloadPhaseChanged OnReloadPhaseChanged;

    [Networked]
    public int reloadPhase { get; set; }
    [SerializeField, HideInInspector]
    private int _previousReloadPhase = -1;

    [SerializeField]
    private WeaponMagazine weaponMag;

    [SerializeField]
    private Animator animator;

    [<PERSON><PERSON>("Magazine")]
    [Serial<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>("This is the magazine inside the weapon mesh that we turn on/off")]
    private GameObject staticMagazine;
    [Serial<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("Magazine prefab that has a rigidboy and gravity")]
    private GameObject physicsMagazinePrefab;
  
    [SerializeField]
    private float droppedMagReturnTime =10;
    [Serial<PERSON><PERSON><PERSON>,Toolt<PERSON>("Time used to delay transition to mag dropped, so we can play anims before turning off animator")]
    private float magazineDropTransitionTime = 0.25f;


    [<PERSON><PERSON>("Reload Components")]
    [Ser<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("The part of weapon where player puts new magazine")]
    private GameObject weaponMagazineCollider;
    [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("The part of the weapon that the player pulls back to cock")]
    private WeaponSlidePullback slideComponent;
    public Transform newMagEntryTransform;

    [Networked]
    public bool releasedMagazine { get; set; }
    private bool spawnedMagLocally = false;

    [Networked] //Dont think this is used anymore 
    private bool newMagAppiled {  get; set; }

    // Cache hashes for animation parameters
    private static readonly int IsFiring = Animator.StringToHash("Fire");
    private static readonly int IsLastBullet = Animator.StringToHash("LastBullet");
    private static readonly int OutOfAmmo = Animator.StringToHash("OutOfAmmo");
    private static readonly int FinishReloadAnim = Animator.StringToHash("FinishReload");
    private static readonly int EjectMagAnim = Animator.StringToHash("EjectMag");

    // Track the last "OutOfAmmo" state to avoid redundant Animator calls
    private bool isOutOfAmmo = false;


    public override void Spawned()
    {
        SetupReloadPhase(reloadPhase);
    }


    public void TryStartReloadProcess()
    {
        // Validate weapon is still equipped before starting reload
        if (!IsWeaponStillEquipped())
        {
            Debug.LogWarning("<color=yellow>TryStartReloadProcess: Weapon is no longer equipped, cannot start reload</color>");
            return;
        }

        if (!weaponMag.ShouldReload())
        {
            Debug.Log("<color=red> WHen trying to reload, weaponMag.ShouldReload() returned false   </color>");
            return;
          }

        if (!WeaponContext.Buttons.IsSet(EInputButton.Reload))
        {
            Debug.Log("<color=green> WHen trying to reload, weaponMag.ShouldReload() returned false   </color>");
            return;
        }


        if (releasedMagazine)
        {
            Debug.Log("<color=blue> WHen trying to reload, releasedMagazine is true   </color>");
            return;
            }

        reloadPhase = 2;
        weaponMag.SetIsReloading();
        releasedMagazine = true;
        newMagAppiled = false;
    }



    public override void FireRender(WeaponContext context)
    {
       
        //If our weapon is down to its last bullet. We switch to play a different fire animaiton,
        //that shows the weapon out of ammo once fired
        if(weaponMag.MagazineAmmo <=0)
        {
            //added this to make sure FinishedReload trigger gets turned off, didnt always happen when unholstering and then reloading
            animator.ResetTrigger(FinishReloadAnim);
            
            animator.SetTrigger(IsFiring);
            animator.SetBool(IsLastBullet, true);
        }
        else
        {
            animator.SetTrigger(IsFiring);
            animator.SetBool(IsLastBullet, false);
        }
       
    }


    public void SetupReloadPhase(int phase)
    {
        //Phase 0 is ready
        //Phase 1 is Out of ammo
        //Phase 2 is Mag released 
        //Phase 3 is new magg applied
        switch (phase)
        {
            case 0:
                releasedMagazine = false;
                //Disable componenets that we dont need active all the time
                weaponMagazineCollider.SetActive(false);
                animator.enabled = true;
                spawnedMagLocally = false;
                animator.SetBool(EjectMagAnim, false);
                animator.SetTrigger(FinishReloadAnim);
                slideComponent.DisableGrab();

                break;           
            case 1:

                break;
            
            case 2:

                break;

            case 3:
                staticMagazine.SetActive(true);
                weaponMagazineCollider.SetActive(false);
                break;
        }


  

    }

    private void UpdateAnimationAmmoState()
    {
        // Check if the weapon is out of ammo
        bool currentOutOfAmmoState = weaponMag.MagazineAmmo <= 0;
        // Update Animator only if the state has changed
        if (currentOutOfAmmoState != isOutOfAmmo)
        {
            isOutOfAmmo = currentOutOfAmmoState;
          animator.SetBool(OutOfAmmo, isOutOfAmmo);

            if(isOutOfAmmo)
                reloadPhase = 1;


        }
    }
        

    public override void Render()
    {
        // Detect reloadPhase changes and trigger the event
        if (reloadPhase != _previousReloadPhase)
        {
             _previousReloadPhase = reloadPhase; // Update stored value

            // Invoke event to notify listeners (e.g., PhysicalReloadTutorial)
            OnReloadPhaseChanged?.Invoke(reloadPhase);
        }

        switch (reloadPhase)
        {
            case 0:
                TryStartReloadProcess();
                UpdateAnimationAmmoState();
                break;
            case 1:
                TryStartReloadProcess();
                UpdateAnimationAmmoState();
                break;
            case 2:
                slideComponent.UpdateSlingPosition();
                if (releasedMagazine)
                {
                    if (spawnedMagLocally)
                        return;

                    StartCoroutine(ReleaseMag());
                    spawnedMagLocally = true;
                }
               
                break;
            case 3:
                slideComponent.UpdateSlingPosition();
                slideComponent.CheckForReloadComplete();
                staticMagazine.SetActive(true);
                weaponMagazineCollider.SetActive(false);
                break;
        }

    }

    private void OnEnable()
    {
        if (isOutOfAmmo)
        {
            animator.SetBool(EjectMagAnim, true);
        }
    }


    private IEnumerator ReleaseMag()
    {
        // Validate that the weapon is still equipped before releasing magazine
        if (!IsWeaponStillEquipped())
        {
            Debug.LogWarning("<color=yellow>ReleaseMag: Weapon is no longer equipped, canceling magazine release</color>");
            // Reset reload state since weapon is no longer equipped
            reloadPhase = 0;
            releasedMagazine = false;
            spawnedMagLocally = false;
            yield break;
        }

        if (Context)
        {
            //Get a mag mesh from pool and set its position
            var droppedMag = Context.ObjectCache.Get(physicsMagazinePrefab);
            droppedMag.transform.position = staticMagazine.transform.position;
            droppedMag.transform.rotation = staticMagazine.transform.rotation;

            //Set a delayed return to the pool for the mesh.
            Context.ObjectCache.ReturnDeferred(droppedMag, droppedMagReturnTime);
        }
        staticMagazine.SetActive(false);
        animator.SetBool(EjectMagAnim, true);

        //Vibrate controller
        if (Object.HasInputAuthority)
            VibrateController();

        //Allow time to spawn ejected mag and finish out of ammo anims before we disbale animator and move to next part of reloading
        yield return new WaitForSeconds(magazineDropTransitionTime);

        weaponMagazineCollider.SetActive(true);
        animator.enabled = false;
        //Allow slide to be grabbable 
        slideComponent.EnableGrab();
    }

    [Rpc(RpcSources.All, RpcTargets.All)]
    public void RPC_ApplyNewMagazine(NetworkId physicalMagazineId)
    {
        releasedMagazine = false;
        newMagAppiled = true;
        reloadPhase = 3;
        staticMagazine.SetActive(true);
        weaponMagazineCollider.SetActive(false);

        if (HasStateAuthority)
        {
            // Try to get the NetworkObject using the NetworkId
            if (Runner.TryFindObject(physicalMagazineId, out var networkObject))
            {
                //Destroy or return this to pool
                Runner.Despawn(networkObject);
            }
            else
            {
                Debug.LogError("Failed to find NetworkObject with the given NetworkId.");
            }
        }


        //Vibrate controller
        if (Object.HasInputAuthority)
            VibrateController();

    }
    public void ApplyNewMagazine(NetworkId physicalMagazine)
    {
        if (Object.HasInputAuthority)
        {
            // Convert the magazine to its NetworkId and send it to the RPC
            RPC_ApplyNewMagazine(physicalMagazine);
        }
    }

    [Rpc(RpcSources.All, RpcTargets.All)]
    public void RPC_FinishReload()
    {
        reloadPhase = 0;
        animator.enabled = true;
        spawnedMagLocally = false;
        weaponMag.FinishReload();
        animator.SetBool(EjectMagAnim, false);
        animator.SetTrigger(FinishReloadAnim);

        slideComponent.DisableGrab();

    }

    public void FinishReload()
    {
        if (Object.HasInputAuthority)
        {
            RPC_FinishReload();
        }

    }

    /// <summary>
    /// Resets the weapon reload state to default (phase 0)
    /// Called when weapon is dropped or ungrabbed
    /// </summary>
    public void ResetReloadState()
    {
        Debug.Log("<color=cyan>ResetReloadState: Resetting weapon reload state from phase " + reloadPhase + " to 0</color>");
        reloadPhase = 0;
        releasedMagazine = false;
        spawnedMagLocally = false;

        // Reset animation state
        if (animator != null)
        {
            animator.SetBool(EjectMagAnim, false);
            animator.SetTrigger(FinishReloadAnim);
            animator.enabled = true;
        }

        // Reset magazine collider
        if (weaponMagazineCollider != null)
        {
            weaponMagazineCollider.SetActive(false);
        }

        // Disable slide grab if available
        if (slideComponent != null)
        {
            slideComponent.DisableGrab();
        }
    }

    /// <summary>
    /// Validates that this weapon is still equipped to a player's hand
    /// </summary>
    private bool IsWeaponStillEquipped()
    {
        // Get the weapon component from this object
        if (!TryGetComponent<Weapon>(out var weapon))
        {
            Debug.LogWarning("<color=yellow>IsWeaponStillEquipped: Weapon component not found</color>");
            return false;
        }

        // Check if weapon has an owning player
        if (weapon.owningPlayer == null)
        {
            Debug.LogWarning("<color=yellow>IsWeaponStillEquipped: Weapon has no owning player</color>");
            return false;
        }

        var playerAgent = weapon.owningPlayer;

        // Check if this weapon is currently equipped in either hand
        bool isEquippedLeft = playerAgent.Weapons.CurrentLeftWeapon == weapon;
        bool isEquippedRight = playerAgent.Weapons.CurrentRightWeapon == weapon;

        bool isEquipped = isEquippedLeft || isEquippedRight;

        if (!isEquipped)
        {
            Debug.LogWarning("<color=yellow>IsWeaponStillEquipped: Weapon " + weapon.name + " is no longer equipped to player " + playerAgent.name + "</color>");
        }

        return isEquipped;
    }
    
    private void VibrateController()
    {
        if (Weapon.equippedHand == EquippedHand.right)
        {
            Weapon.owningPlayer.hardwareRig.rightHand.StartVibration(1, 1, .2f);
        }
        else if (Weapon.equippedHand == EquippedHand.left)
        {
            Weapon.owningPlayer.hardwareRig.leftHand.StartVibration(1, 1, .2f);

        }
    }
}
