using UnityEngine;
using Fusion;
using Projectiles;
using System.Collections.Generic;
using UnityEngine.Animations.Rigging;

public class WeaponPhysicalMagazine : NetworkBehaviour
{

    [SerializeField]
    private NetworkObject networkObject;
    private Collider lastCheckedCollider;
    private WeaponAnimator lastCheckColliderWeapon;


    public NetworkPhysicsGrabbable networkPhysicsGrabbable;
    [SerializeField]
    private Rigidbody rb;

    [SerializeField, HideInInspector]
    private PlayerAgent owningPlayer;
    [Networked]
    private NetworkId _playerNetworkId { get; set; } // Store the owner's ID


    //Line UI
    [SerializeField]
    private LineRendererToObject lineToWeaponRenderer;
    private bool isGrabbed = false;
    [SerializeField]
    private Transform lineStartPoint;

    //Despawning
    private float despawnTimer = 0f;
    private const float DespawnTime = 5f; // Time before despawning
    private bool isCountingDown = false; // Flag to track countdown state

    private void Awake()
    {
        Debug.Assert(networkPhysicsGrabbable, "No networkPhysicsGrabbable assigned to WeaponPhysicalMagazine in " + this.gameObject.name);
        DisableGravity();
    }
    private void OnEnable()
    {
        DisableGravity();
        // Clear connection tracking when object is enabled
        weaponAttemptTimes.Clear();
    }
    public void Init(NetworkId playerNetworkId)
    {
        if (HasStateAuthority)
        {
            _playerNetworkId = playerNetworkId;
        }
    }


    // Collision optimization variables
    private float lastConnectionAttemptTime = 0f;
    private const float CONNECTION_COOLDOWN = 0.1f; // 100ms cooldown between connection attempts

    // Track connection attempts per weapon to allow multiple connections
    private Dictionary<WeaponAnimator, float> weaponAttemptTimes = new Dictionary<WeaponAnimator, float>();

    [SerializeField]
    private LayerMask weaponLayerMask = -1; // Default to all layers, set in inspector to filter



    private void OnCollisionEnter(Collision collision)
    {
        // Check if the collision is with a relevant layer (if specified)
        if (weaponLayerMask != -1 && ((1 << collision.gameObject.layer) & weaponLayerMask) == 0)
            return;

        // Global cooldown to prevent excessive checks
        if (Time.time - lastConnectionAttemptTime < CONNECTION_COOLDOWN)
            return;

        TryConnectToWeapon(collision);
    }

    // We still need OnCollisionStay but with optimizations
    private void OnCollisionStay(Collision collision)
    {
        // Check if the collision is with a relevant layer (if specified)
        if (weaponLayerMask != -1 && ((1 << collision.gameObject.layer) & weaponLayerMask) == 0)
            return;

        // Global cooldown to prevent excessive checks
        if (Time.time - lastConnectionAttemptTime < CONNECTION_COOLDOWN)
            return;

        TryConnectToWeapon(collision);
    }

    private void TryConnectToWeapon(Collision collision)
    {
        lastConnectionAttemptTime = Time.time;

        WeaponAnimator item;

        if (lastCheckedCollider == collision.collider)
        {
            item = lastCheckColliderWeapon;
        }
        else
        {
            // Use a more efficient way to find the WeaponAnimator
            // First try direct GetComponent which is faster
            item = collision.collider.GetComponent<WeaponAnimator>();

            // If that fails, then try GetComponentInParent which is more expensive
            if (item == null)
            {
                item = collision.collider.GetComponentInParent<WeaponAnimator>();
            }
        }

        lastCheckedCollider = collision.collider;
        lastCheckColliderWeapon = item;

        if (item != null)
        {
            // Check if we've recently tried to connect to this specific weapon
            if (weaponAttemptTimes.TryGetValue(item, out float lastAttemptTime))
            {
                // If we've tried recently with this specific weapon, skip
                if (Time.time - lastAttemptTime < 1.0f) // 1 second cooldown per weapon
                    return;
            }

            // Update the last attempt time for this weapon
            weaponAttemptTimes[item] = Time.time;

            if (networkObject.HasInputAuthority)
            {
                item.ApplyNewMagazine(networkObject.Id);
            }
        }
    }



    public override void Spawned()
    {



        ResetDespawnTimer(); // Ensures timer is fresh when the object spawns
                             // Only local player should resolve it



        if (Object.HasInputAuthority)
        {
            TryResolvePlayer();

            MagazineRegistry.Register(Object.InputAuthority, Object);
            Debug.Log($"[MAG REGISTRY] Client registered mag for: {Object.InputAuthority}");


        }

        // Start countdown to despawn
        if (!isCountingDown)
        {
            isCountingDown = true;
            despawnTimer = DespawnTime;
        }
    }

    public void EnableGravity()
    {
        networkPhysicsGrabbable.pauseFollow = false;
        rb.isKinematic = false;
        rb.useGravity = true;
    }

    public void DisableGravity()
    {
        networkPhysicsGrabbable.pauseFollow = true;
        rb.isKinematic = true;
        rb.useGravity = false;
    }

    public void OnUngrab()
    {
        isGrabbed = false;
        // Start countdown to despawn
        if (!isCountingDown)
        {
            isCountingDown = true;
            despawnTimer = DespawnTime;
        }

        if (Object.HasInputAuthority)
            lineToWeaponRenderer.HideLineRendererToObject();

    }

    public void OnGrab()
    {
        isGrabbed = true;
        // Stop and reset the countdown
        ResetDespawnTimer();

        EnableGravity();

        //UI line rendererer to magazine slot on weapon
        InitLineRendererUI();
    }

    private void Update()
    {
        if (isGrabbed)
        {
            if (Object.HasInputAuthority)
                lineToWeaponRenderer.UpdateLineRendererToObject();
        }
    }

    public void InitLineRendererUI()
    {
        if (!Object.HasInputAuthority || networkPhysicsGrabbable.CurrentGrabber == null)
            return;

        if (owningPlayer == null)
        {
            Debug.LogWarning("<color=yellow>InitLineRendererUI: owningPlayer is null</color>");
            return;
        }

        // Determine which weapon we should reference - FIXED: was using opposite hand logic
        bool isLeftHand = networkPhysicsGrabbable.CurrentGrabber.hand.side == RigPart.LeftController;
        var currentWeapon = isLeftHand ? owningPlayer.Weapons.CurrentLeftWeapon : owningPlayer.Weapons.CurrentRightWeapon;

        if (currentWeapon == null)
        {
            Debug.LogWarning("<color=yellow>InitLineRendererUI: No weapon equipped in the grabbing hand</color>");
            return;
        }

        // Verify the weapon is actually equipped to the grabbing hand
        var grabbingHand = networkPhysicsGrabbable.CurrentGrabber.hand;
        if (grabbingHand.currentWeapon != currentWeapon)
        {
            Debug.LogWarning("<color=yellow>InitLineRendererUI: Weapon mismatch - hand weapon doesn't match expected weapon</color>");
            return;
        }

        // Check if weapon has a magazine system
        if (currentWeapon.weaponMagazine == null || currentWeapon.weaponMagazine.weaponAnimator == null)
        {
            Debug.LogWarning("<color=yellow>InitLineRendererUI: Weapon missing magazine or animator component</color>");
            return;
        }

        // Check if in correct physical reload phase and if not dont show UI
        if (currentWeapon.weaponMagazine.weaponAnimator.reloadPhase != 2)
        {
            Debug.LogWarning("<color=cyan>InitLineRendererUI: Weapon not in reload phase 2 (current phase: " + currentWeapon.weaponMagazine.weaponAnimator.reloadPhase + ")</color>");
            return;
        }

        // Get the target transform, defaulting to the weapon object if null
        Transform targetLineEndTransform = currentWeapon.weaponMagazine.weaponAnimator.newMagEntryTransform ?? currentWeapon.gameObject.transform;

        if (targetLineEndTransform != null)
        {
            Debug.Log("<color=green>InitLineRendererUI: Showing line renderer to weapon: " + currentWeapon.name + "</color>");
            lineToWeaponRenderer.ShowLineRenderer(lineStartPoint, targetLineEndTransform);
        }
        else
        {
            Debug.LogWarning("<color=yellow>InitLineRendererUI: Target transform is null</color>");
        }
    }

    public override void FixedUpdateNetwork()
    {
        if (Object.HasInputAuthority && owningPlayer == null)
        {
            TryResolvePlayer();
        }

        if (isCountingDown)
        {
            despawnTimer -= Runner.DeltaTime; // Decrease timer based on network time

            if (despawnTimer <= 0f)
            {
                isCountingDown = false;
                if (HasStateAuthority) // Only the state authority should despawn the object
                {
                    Runner.Despawn(networkObject);
                    ResetDespawnTimer(); // Ensure timer is reset when despawning
                }
            }
        }

    }

    public override void Despawned(NetworkRunner runner, bool hasState)
    {
        if (HasInputAuthority)
        {
            MagazineRegistry.Unregister(Object.InputAuthority);
        }
    }

    private void ResetDespawnTimer()
    {
        isCountingDown = false;
        despawnTimer = 0f;
    }
    private void TryResolvePlayer()
    {
        if (owningPlayer == null && Runner.TryFindObject(_playerNetworkId, out var playerObject))
        {
            owningPlayer = playerObject.GetComponentInParent<PlayerAgent>();

        }
    }



}
